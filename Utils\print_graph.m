function varargout = print_graph(neighbor_lists)
    % PRINT_GRAPH Visualizes a network graph from neighbor lists
    %
    % Input:
    %   neighbor_lists - Cell array where each cell contains neighbor indices
    %                   for that agent (including self-connections)
    %
    % Example:
    %   neighbor_lists = {[1,2,3]; [1,2]; [1,3,4]; [3,4,5,6]; [4,5]; [4,6]};
    %   print_graph(neighbor_lists);
    
    % Input validation
    if ~iscell(neighbor_lists)
        error('Input must be a cell array');
    end
    
    if isempty(neighbor_lists)
        error('Input cell array cannot be empty');
    end
    
    % Initialize source and target arrays
    s = [];
    t = [];
    
    % Convert neighbor lists to edge arrays
    for i = 1:length(neighbor_lists)
        neighbors = neighbor_lists{i};
        
        % Validate that neighbors is a numeric array
        if ~isnumeric(neighbors)
            error('Each cell must contain a numeric array of neighbor indices');
        end
        
        % Add edges from agent i to each of its neighbors
        for j = 1:length(neighbors)
            s = [s, i];
            t = [t, neighbors(j)];
        end
    end
    
    % Create graph object
    G = graph(s, t);
    
    % Plot the graph
    plot(G);
    title('Network Graph');
    xlabel('Node');
    ylabel('Node');

    if nargout == 1
        varargout{1} = s;
    elseif nargout == 2
        varargout{1} = s;
        varargout{2} = t;
    end
end
