function varargout = print_graph(neighbor_lists)
    % PRINT_GRAPH Visualizes a network graph from neighbor lists
    %
    % Input:
    %   neighbor_lists - Cell array where each cell contains neighbor indices
    %                   for that agent (including self-connections)
    %
    % Example:
    %   neighbor_lists = {[1,2,3]; [1,2]; [1,3,4]; [3,4,5,6]; [4,5]; [4,6]};
    %   print_graph(neighbor_lists);
    
    % Input validation
    if ~iscell(neighbor_lists)
        error('Input must be a cell array');
    end
    
    if isempty(neighbor_lists)
        error('Input cell array cannot be empty');
    end
    
    % Initialize source and target arrays
    s = [];
    t = [];
    
    % Convert neighbor lists to edge arrays
    for i = 1:length(neighbor_lists)
        neighbors = neighbor_lists{i};
        
        % Validate that neighbors is a numeric array
        if ~isnumeric(neighbors)
            error('Each cell must contain a numeric array of neighbor indices');
        end
        
        % Add edges from agent i to each of its neighbors
        for j = 1:length(neighbors)
            s = [s, i];
            t = [t, neighbors(j)];
        end
    end
    
    % Create edge lookup for fast bidirectional checking
    edge_set = containers.Map();
    for idx = 1:length(s)
        key = sprintf('%d_%d', s(idx), t(idx));
        edge_set(key) = true;
    end

    % Classify edges by connection type
    self_edges = false(size(s));
    bidirectional_edges = false(size(s));
    unidirectional_edges = false(size(s));

    for idx = 1:length(s)
        if s(idx) == t(idx)
            % Self-connection
            self_edges(idx) = true;
        else
            % Check if reverse edge exists
            reverse_key = sprintf('%d_%d', t(idx), s(idx));
            if isKey(edge_set, reverse_key)
                % Bidirectional connection
                bidirectional_edges(idx) = true;
            else
                % Unidirectional connection
                unidirectional_edges(idx) = true;
            end
        end
    end

    % Create graph object (directed to support arrows)
    G = digraph(s, t);

    % Plot the graph with customizations
    h = plot(G, 'NodeFontSize', 14, 'MarkerSize', 10, 'ArrowSize', 0);

    % Highlight different edge types
    % Self-connections in red
    if any(self_edges)
        highlight(h, s(self_edges), t(self_edges), 'EdgeColor', 'red', 'LineWidth', 2);
    end

    % Bidirectional connections in blue (no arrows)
    if any(bidirectional_edges)
        highlight(h, s(bidirectional_edges), t(bidirectional_edges), 'EdgeColor', 'blue', 'LineWidth', 2, 'ArrowSize', 0);
    end

    % Unidirectional connections in green (with arrows)
    if any(unidirectional_edges)
        highlight(h, s(unidirectional_edges), t(unidirectional_edges), 'EdgeColor', 'green', 'LineWidth', 2, 'ArrowSize', 10);
    end

    % Customize appearance
    title('Network Graph', 'FontSize', 16);
    xlabel('Node', 'FontSize', 12);
    ylabel('Node', 'FontSize', 12);

    if nargout == 1
        varargout{1} = s;
    elseif nargout == 2
        varargout{1} = s;
        varargout{2} = t;
    end
end
